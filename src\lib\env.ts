/**
 * Environment variable validation and type safety
 * This ensures all required environment variables are present and valid
 */

// Define the shape of our environment variables
interface EnvironmentVariables {
  NEXT_PUBLIC_BASE_URL: string;
  NODE_ENV: 'development' | 'production' | 'test';
  ANALYZE?: string;
}

// Validation functions
const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

const isValidNodeEnv = (env: string): env is 'development' | 'production' | 'test' => {
  return ['development', 'production', 'test'].includes(env);
};

// Get and validate environment variables
function getEnvironmentVariables(): EnvironmentVariables {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
  const nodeEnv = process.env.NODE_ENV;
  const analyze = process.env.ANALYZE;

  // Validate required variables
  if (!baseUrl) {
    console.warn('NEXT_PUBLIC_BASE_URL is not set, using default: https://example.com');
  } else if (!isValidUrl(baseUrl)) {
    throw new Error(`NEXT_PUBLIC_BASE_URL must be a valid URL, got: ${baseUrl}`);
  }

  if (!nodeEnv || !isValidNodeEnv(nodeEnv)) {
    throw new Error(`NODE_ENV must be one of: development, production, test. Got: ${nodeEnv}`);
  }

  return {
    NEXT_PUBLIC_BASE_URL: baseUrl || 'https://example.com',
    NODE_ENV: nodeEnv,
    ANALYZE: analyze,
  };
}

// Export validated environment variables
export const env = getEnvironmentVariables();

// Helper functions for common environment checks
export const isDevelopment = env.NODE_ENV === 'development';
export const isProduction = env.NODE_ENV === 'production';
export const isTest = env.NODE_ENV === 'test';
export const shouldAnalyze = env.ANALYZE === 'true';

// Base URL with trailing slash removed
export const baseUrl = env.NEXT_PUBLIC_BASE_URL.replace(/\/$/, '');

// Type-safe environment variable access
export function getEnvVar(key: keyof EnvironmentVariables): string {
  const value = env[key];
  if (value === undefined) {
    throw new Error(`Environment variable ${key} is not defined`);
  }
  return value;
}

// Optional environment variable access
export function getOptionalEnvVar(key: keyof EnvironmentVariables): string | undefined {
  return env[key];
}

export default env;
