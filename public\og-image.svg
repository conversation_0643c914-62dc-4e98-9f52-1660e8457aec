<svg width="1200" height="630" viewBox="0 0 1200 630" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#151156;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4D528E;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="rgba(0,0,0,0.25)"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="630" fill="url(#bgGradient)"/>
  
  <!-- Decorative Elements -->
  <circle cx="100" cy="100" r="60" fill="rgba(255,255,255,0.1)"/>
  <circle cx="1100" cy="530" r="80" fill="rgba(255,255,255,0.08)"/>
  <rect x="950" y="50" width="200" height="200" rx="20" fill="rgba(255,255,255,0.05)" transform="rotate(15 1050 150)"/>
  
  <!-- Main Content Container -->
  <g transform="translate(600, 315)">
    <!-- Main Title -->
    <text x="0" y="-60" text-anchor="middle" fill="white" font-family="Inter, system-ui, sans-serif" font-size="72" font-weight="700" filter="url(#shadow)">
      Landing Template
    </text>
    
    <!-- Subtitle -->
    <text x="0" y="20" text-anchor="middle" fill="rgba(255,255,255,0.9)" font-family="Inter, system-ui, sans-serif" font-size="32" font-weight="400">
      Modern &amp; Fast Landing Pages
    </text>
    
    <!-- Tech Stack -->
    <g transform="translate(0, 80)">
      <!-- Next.js Badge -->
      <g transform="translate(-180, 0)">
        <rect x="-50" y="-15" width="100" height="30" rx="15" fill="rgba(255,255,255,0.2)"/>
        <text x="0" y="5" text-anchor="middle" fill="white" font-family="Inter, system-ui, sans-serif" font-size="14" font-weight="500">
          Next.js 15
        </text>
      </g>
      
      <!-- React Badge -->
      <g transform="translate(-60, 0)">
        <rect x="-35" y="-15" width="70" height="30" rx="15" fill="rgba(255,255,255,0.2)"/>
        <text x="0" y="5" text-anchor="middle" fill="white" font-family="Inter, system-ui, sans-serif" font-size="14" font-weight="500">
          React 19
        </text>
      </g>
      
      <!-- Tailwind Badge -->
      <g transform="translate(60, 0)">
        <rect x="-45" y="-15" width="90" height="30" rx="15" fill="rgba(255,255,255,0.2)"/>
        <text x="0" y="5" text-anchor="middle" fill="white" font-family="Inter, system-ui, sans-serif" font-size="14" font-weight="500">
          Tailwind CSS
        </text>
      </g>
      
      <!-- TypeScript Badge -->
      <g transform="translate(180, 0)">
        <rect x="-45" y="-15" width="90" height="30" rx="15" fill="rgba(255,255,255,0.2)"/>
        <text x="0" y="5" text-anchor="middle" fill="white" font-family="Inter, system-ui, sans-serif" font-size="14" font-weight="500">
          TypeScript
        </text>
      </g>
    </g>
  </g>
  
  <!-- Bottom Features -->
  <g transform="translate(600, 550)">
    <text x="0" y="0" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-family="Inter, system-ui, sans-serif" font-size="18" font-weight="400">
      🚀 Optimized • 📱 Responsive • 🔍 SEO-Ready • ♿ Accessible
    </text>
  </g>
</svg>
