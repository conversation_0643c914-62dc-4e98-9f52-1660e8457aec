'use client';

import React from "react";
import Container from "../Container";

// Header Navigation Component
export function HeaderNav() {
  const [atTop, setAtTop] = React.useState(true);

  React.useEffect(() => {
    const onScroll = () => {
      setAtTop(window.scrollY <= 0);
    };
    onScroll();
    window.addEventListener('scroll', onScroll, { passive: true });
    return () => window.removeEventListener('scroll', onScroll);
  }, []);

  const headerClass =
    "sticky top-0 z-[9999] transition-colors" +
    (atTop
      ? " bg-transparent supports-[backdrop-filter]:bg-transparent backdrop-blur-0 border-b border-transparent"
      : " backdrop-blur-2xl supports-[backdrop-filter]:bg-background/95 bg-background/95 border-b border-card-border");

  return (
    <header className={headerClass}>
      <Container className="h-14 flex items-center justify-between">
        <div className="font-heading font-semibold" role="banner">
          <a href="#hero" className="focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-brand-primary rounded" aria-label="Brand - Go to homepage">
            Brand
          </a>
        </div>
        <nav className="hidden sm:flex items-center gap-6 text-sm" role="navigation" aria-label="Main navigation">
          <a href="#benefits" className="hover:underline focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-brand-primary rounded" aria-label="Go to Benefits section">Benefits</a>
          <a href="#location" className="hover:underline focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-brand-primary rounded" aria-label="Go to Location section">Location</a>
          <a href="#bestsellers" className="hover:underline focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-brand-primary rounded" aria-label="Go to Best Sellers section">Best Sellers</a>
          <a href="#testimonials" className="hover:underline focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-brand-primary rounded" aria-label="Go to Testimonials section">Testimonials</a>
          <a href="#about" className="hover:underline focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-brand-primary rounded" aria-label="Go to About Us section">About Us</a>
          <a href="#gallery" className="hover:underline focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-brand-primary rounded" aria-label="Go to Gallery section">Gallery</a>
          <a href="#faq" className="hover:underline focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-brand-primary rounded" aria-label="Go to FAQ section">FAQ</a>
        </nav>
        <div className="flex items-center gap-3">
          <a
            href="#contact"
            className="inline-flex h-9 items-center justify-center rounded-md bg-primary-button-bg text-primary-button-text px-4 text-sm hover:bg-primary-button-hover transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-primary-button-bg"
            aria-label="Go to Contact section"
          >
            Contact
          </a>
        </div>
      </Container>
    </header>
  );
}

// Site Footer Component
export function SiteFooter() {
  return (
    <footer className="border-t py-10 mt-10" role="contentinfo">
      <Container className="flex flex-col sm:flex-row items-center justify-between gap-4">
        <div className="text-sm text-muted-foreground-light">
          © {new Date().getFullYear()} Brand. All rights reserved.
        </div>
        <nav className="flex items-center gap-4 text-sm" role="navigation" aria-label="Footer navigation">
          <a
            className="hover:underline focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-brand-primary rounded"
            href="#"
            aria-label="Privacy Policy"
          >
            Privacy
          </a>
          <a
            className="hover:underline focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-brand-primary rounded"
            href="#"
            aria-label="Terms of Service"
          >
            Terms
          </a>
          <a
            className="hover:underline focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-brand-primary rounded"
            href="#contact"
            aria-label="Contact Us"
          >
            Contact
          </a>
        </nav>
      </Container>
    </footer>
  );
}
