import React from 'react';
import { baseUrl } from '@/lib/env';

interface StructuredDataProps {
  data: object;
}

export function StructuredData({ data }: StructuredDataProps) {
  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(data),
      }}
    />
  );
}

// Website structured data
export const websiteStructuredData = {
  "@context": "https://schema.org",
  "@type": "WebSite",
  "name": "Landing Template",
  "description": "A modern landing page template built with Next.js 15 and Tailwind CSS. Responsive, fast, SEO-friendly, and easy to customize for your business.",
  "url": baseUrl,
  "potentialAction": {
    "@type": "SearchAction",
    "target": {
      "@type": "EntryPoint",
      "urlTemplate": `${baseUrl}/?q={search_term_string}`
    },
    "query-input": "required name=search_term_string"
  }
};

// Organization structured data
export const organizationStructuredData = {
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "Landing Template",
  "description": "Modern landing page templates for businesses",
  "url": baseUrl,
  "logo": `${baseUrl}/favicon.svg`,
  "contactPoint": {
    "@type": "ContactPoint",
    "contactType": "customer service",
    "availableLanguage": ["English"]
  }
};

// FAQ structured data
export const faqStructuredData = {
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": [
    {
      "@type": "Question",
      "name": "Can it be deployed to Vercel?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Yes, Next.js is an excellent fit for Vercel and can be deployed easily."
      }
    },
    {
      "@type": "Question",
      "name": "Is it easy to customize?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "All components are built modularly and are easy to customize to suit your brand needs."
      }
    },
    {
      "@type": "Question",
      "name": "Is it SEO-friendly?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Yes, this template is built with complete meta tags and modern Next.js SEO practices."
      }
    }
  ]
};
