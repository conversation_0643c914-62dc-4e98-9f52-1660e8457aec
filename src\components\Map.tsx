'use client';

import React, { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';

// Dynamically import the map component to avoid SSR issues
const MapComponent = dynamic(() => import('./MapComponent'), {
  ssr: false,
  loading: () => (
    <div className="w-full h-64 bg-card-bg border border-card-border rounded-lg flex items-center justify-center">
      <div className="text-muted-foreground text-sm">Loading map...</div>
    </div>
  ),
});

interface MapProps {
  latitude: number;
  longitude: number;
  address: string;
  className?: string;
}

export default function Map({ latitude, longitude, address, className = "" }: MapProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className={`w-full h-64 bg-card-bg border border-card-border rounded-lg flex items-center justify-center ${className}`}>
        <div className="text-muted-foreground text-sm">Loading map...</div>
      </div>
    );
  }

  return (
    <div className={className}>
      <MapComponent 
        latitude={latitude} 
        longitude={longitude} 
        address={address} 
      />
    </div>
  );
}
