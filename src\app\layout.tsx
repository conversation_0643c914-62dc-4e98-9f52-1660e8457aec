import type { Metadata } from "next";
import { Inter, Poppins } from "next/font/google";
import "./globals.css";
import { StructuredData, websiteStructuredData, organizationStructuredData } from "@/components/StructuredData";
import { ErrorBoundary } from "@/components/ErrorBoundary";
import { SkipToContent } from "@/components/SkipToContent";
import { baseUrl } from "@/lib/env";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: "swap",
  preload: true,
  fallback: ['system-ui', 'arial'],
});

const poppins = Poppins({
  variable: "--font-poppins",
  weight: ["400", "500", "600", "700"],
  subsets: ["latin"],
  display: "swap",
  preload: true,
  fallback: ['system-ui', 'arial'],
});

// baseUrl is now imported from env validation

export const metadata: Metadata = {
  title: {
    default: "Landing Template - Modern & Fast Landing Pages",
    template: "%s | Landing Template"
  },
  description: "A modern landing page template built with Next.js 15 and Tailwind CSS. Responsive, fast, SEO-friendly, and easy to customize for your business.",
  keywords: ["landing page", "next.js", "tailwind css", "template", "responsive", "modern", "fast", "SEO"],
  authors: [{ name: "Landing Template Team" }],
  creator: "Landing Template",
  publisher: "Landing Template",
  metadataBase: new URL(baseUrl),
  openGraph: {
    title: "Landing Template - Modern & Fast Landing Pages",
    description: "A modern landing page template built with Next.js 15 and Tailwind CSS. Responsive, fast, SEO-friendly, and easy to customize for your business.",
    url: '/',
    siteName: 'Landing Template',
    type: 'website',
    locale: 'en_US',
    images: [
      {
        url: '/og-image.svg',
        width: 1200,
        height: 630,
        alt: 'Landing Template - Modern Landing Page built with Next.js 15, React 19, and Tailwind CSS',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: "Landing Template - Modern & Fast Landing Pages",
    description: "A modern landing page template built with Next.js 15 and Tailwind CSS. Responsive, fast, SEO-friendly, and easy to customize for your business.",
    images: ['/og-image.svg'],
  },
  icons: {
    icon: '/sui icon.jpg',
    shortcut: '/sui icon.jpg',
    apple: '/sui icon.jpg',
  },
  alternates: {
    canonical: '/',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        {/* Font preloading for better performance */}
        <link
          rel="preconnect"
          href="https://fonts.googleapis.com"
        />
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="anonymous"
        />

        {/* Structured Data for SEO */}
        <StructuredData data={websiteStructuredData} />
        <StructuredData data={organizationStructuredData} />


      </head>
      <body className={`${inter.variable} ${poppins.variable} antialiased`}>
        <SkipToContent />
        <ErrorBoundary>
          {children}
        </ErrorBoundary>
      </body>
    </html>
  );
}
