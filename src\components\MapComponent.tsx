'use client';

import React, { useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

// Fix for default markers in react-leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

interface MapComponentProps {
  latitude: number;
  longitude: number;
  address: string;
}

export default function MapComponent({ latitude, longitude, address }: MapComponentProps) {
  const mapRef = useRef<L.Map | null>(null);

  useEffect(() => {
    // Custom CSS for map styling
    const style = document.createElement('style');
    style.textContent = `
      .leaflet-container {
        background: var(--card-bg);
        border-radius: 0.5rem;
      }

      .leaflet-popup-content-wrapper {
        background: var(--card-bg);
        color: var(--foreground);
        border: 1px solid var(--card-border);
      }

      .leaflet-popup-tip {
        background: var(--card-bg);
        border: 1px solid var(--card-border);
      }

      .leaflet-control-zoom a {
        background-color: var(--card-bg);
        border: 1px solid var(--card-border);
        color: var(--foreground);
      }

      .leaflet-control-zoom a:hover {
        background-color: var(--secondary-button-hover);
      }
      
      .leaflet-control-attribution {
        background-color: var(--card-bg);
        border: 1px solid var(--card-border);
        color: var(--muted-foreground);
      }
      
      .leaflet-control-attribution a {
        color: var(--foreground);
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  return (
    <div className="w-full h-64 rounded-lg overflow-hidden border border-card-border">
      <MapContainer
        center={[latitude, longitude]}
        zoom={15}
        style={{ height: '100%', width: '100%' }}
        ref={mapRef}
        scrollWheelZoom={false}
        zoomControl={true}
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />
        <Marker position={[latitude, longitude]}>
          <Popup>
            <div className="text-sm">
              <strong className="font-medium">{address}</strong>
            </div>
          </Popup>
        </Marker>
      </MapContainer>
    </div>
  );
}
