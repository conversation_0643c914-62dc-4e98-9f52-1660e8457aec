import React from "react";
import Container from "../Container";
import Map from "../Map";

// Hero Section Component
export function Hero() {
  return (
    <section className="py-16 sm:py-24 bg-hero-gradient" aria-labelledby="hero-heading">
      <Container className="text-center">
        <div className="inline-flex items-center rounded-full border border-white/20 bg-white/10 px-3 py-1 text-xs font-medium gap-2 text-white" role="status" aria-label="New template announcement">
          <span className="inline-block h-2 w-2 rounded-full bg-white" aria-hidden="true" />
          New template • Next.js + Tailwind
        </div>
        <h1 id="hero-heading" className="mt-6 text-4xl sm:text-5xl md:text-6xl font-heading font-semibold tracking-tight text-white drop-shadow-lg">
          Make your brand stand out with a modern landing page
        </h1>
        <p className="mt-4 text-base sm:text-lg text-white/95 max-w-2xl mx-auto drop-shadow-md">
          Responsive, fast, and easy to customize. Perfect for businesses, products, or campaigns.
        </p>
        <div className="mt-8 flex items-center justify-center gap-3" role="group" aria-label="Call to action buttons">
          <a
            href="#contact"
            className="inline-flex h-11 items-center justify-center rounded-md bg-white text-brand-primary px-5 text-sm font-medium hover:bg-white/90 transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-white shadow-lg"
            aria-label="Start using the template now"
          >
            Get Started
          </a>
          <a
            href="#gallery"
            className="inline-flex h-11 items-center justify-center rounded-md border-2 border-white bg-transparent text-white px-5 text-sm font-medium hover:bg-white hover:text-brand-primary transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-white"
            aria-label="View template demo and examples"
          >
            View Demo
          </a>
        </div>
      </Container>
    </section>
  );
}

// Key Benefits Section Component
const benefits = [
  {
    title: "Fast & SEO-friendly",
    desc: "Built with Next.js 15 and modern optimizations.",
  },
  {
    title: "Easy to customize",
    desc: "Tailwind CSS enables consistent styling.",
  },
  {
    title: "Responsive",
    desc: "Optimal display on all devices.",
  },
];

export function KeyBenefits() {
  return (
    <section className="py-16 sm:py-20">
      <Container>
        <h2 className="text-2xl sm:text-3xl font-heading font-semibold">Key Benefits</h2>
        <div className="mt-8 grid gap-6 sm:grid-cols-3">
          {benefits.map((b) => (
            <div key={b.title} className="rounded-xl border border-card-border p-6 bg-card-bg">
              <h3 className="font-heading font-semibold text-lg">{b.title}</h3>
              <p className="mt-2 text-sm text-muted-foreground">{b.desc}</p>
            </div>
          ))}
        </div>
      </Container>
    </section>
  );
}

// About Section Component
const aboutItems = [
  {
    id: "about-mission",
    title: "Mission",
    content: "Helping businesses grow through fast, beautiful, and effective websites."
  },
  {
    id: "about-motto",
    title: "Motto",
    content: "\"Simple, Fast, Quality\""
  },
  {
    id: "about-specialties",
    title: "Specialties",
    content: "Modern Landing Pages • SEO Optimization • High Performance"
  },
  {
    id: "about-experience",
    title: "Experience",
    content: "5+ years in web development and user-friendly UI/UX design."
  }
];

export function About() {
  return (
    <section className="py-16 sm:py-20">
      <Container>
        <h2 className="text-2xl sm:text-3xl font-heading font-semibold">About Us</h2>
        <div className="mt-6 grid gap-8 lg:grid-cols-3 items-start">
          {/* Left column - 1/3 width */}
          <div className="lg:col-span-1">
            <p className="text-sm text-muted-foreground leading-relaxed">
              We are a team focused on user experience and performance.
              This template is designed to help you launch a high-quality landing page
              with cutting-edge technology and modern design.
            </p>
          </div>

          {/* Right column - 2/3 width - Stacked items like FAQ */}
          <div className="lg:col-span-2 grid gap-4">
            {aboutItems.map((item) => (
              <div
                key={item.id}
                className="rounded-lg border border-card-border bg-card-bg p-4 group"
              >
                <h3 className="font-medium">
                  {item.title}
                </h3>
                <div className="mt-2">
                  <p className="text-sm text-muted-foreground">
                    {item.content}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </Container>
    </section>
  );
}

// Location & Hours Section Component
export function LocationHours() {
  // Jakarta coordinates for demo - replace with actual business location
  const businessLocation = {
    latitude: -6.2088,
    longitude: 106.8456,
    address: "Jl. Contoh No. 123, Jakarta, Indonesia"
  };

  return (
    <section className="py-16 sm:py-20">
      <Container>
        <h2 className="text-2xl sm:text-3xl font-heading font-semibold">Location & Hours</h2>

        {/* Information Section */}
        <div className="mt-6 grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
          <div className="rounded-xl border border-card-border p-6 bg-card-bg">
            <h3 className="font-heading font-semibold text-brand-primary">Address</h3>
            <p className="mt-2 text-sm text-muted-foreground">
              {businessLocation.address}
            </p>
          </div>

          <div className="rounded-xl border border-card-border p-6 bg-card-bg">
            <h3 className="font-heading font-semibold text-brand-primary">Opening Hours</h3>
            <ul className="mt-2 text-sm text-muted-foreground space-y-1">
              <li>Monday - Friday: 09:00 - 18:00</li>
              <li>Saturday: 10:00 - 16:00</li>
              <li>Sunday & Public Holidays: Closed</li>
            </ul>
          </div>

          <div className="rounded-xl border border-card-border p-6 bg-card-bg sm:col-span-2 lg:col-span-1">
            <h3 className="font-heading font-semibold text-brand-primary">Contact</h3>
            <div className="mt-2 text-sm text-muted-foreground space-y-1">
              <p>📞 +62 21 1234 5678</p>
              <p>✉️ <EMAIL></p>
              <div className="mt-3">
                <a
                  href={`https://www.google.com/maps?q=${businessLocation.latitude},${businessLocation.longitude}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex h-9 items-center justify-center rounded-md border border-secondary-button-border bg-secondary-button-bg text-secondary-button-text px-3 text-xs hover:bg-secondary-button-hover transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-secondary-button-border"
                >
                  View on Google Maps
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Map Section */}
        <div className="mt-8">
          <Map
            latitude={businessLocation.latitude}
            longitude={businessLocation.longitude}
            address={businessLocation.address}
            className="w-full h-[400px] rounded-xl overflow-hidden border border-card-border"
          />
        </div>
      </Container>
    </section>
  );
}
