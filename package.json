{"name": "landing-template", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "analyze": "cross-env ANALYZE=true npm run build", "analyze:server": "cross-env BUNDLE_ANALYZE=server npm run build", "analyze:browser": "cross-env BUNDLE_ANALYZE=browser npm run build"}, "dependencies": {"@types/leaflet": "^1.9.20", "leaflet": "^1.9.4", "next": "15.4.6", "react": "19.1.0", "react-dom": "19.1.0", "react-leaflet": "^5.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/bundle-analyzer": "^15.4.6", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "cross-env": "^10.0.0", "eslint": "^9", "eslint-config-next": "15.4.6", "tailwindcss": "^4", "typescript": "^5"}}