@import "tailwindcss";

:root {
  /* New single color theme based on provided palette */
  --background: #F8F9FC; /* Page background / large surfaces (very light) */
  --foreground: #0D0D2A; /* Primary text / very dark accents */
  --muted-foreground: #0D0D2A; /* Body text - same as foreground for consistency */
  --muted-foreground-light: #4D528E; /* Secondary text / muted content */

  /* Brand colors */
  --brand-primary: #151156; /* Brand color / primary UI color */
  --brand-secondary: #4D528E; /* Secondary / section backgrounds / muted cards */
  --brand-accent: #A4C6EC; /* Accent / highlights / subtle UI elements */

  /* Button colors */
  --primary-button-bg: #151156; /* Brand primary for CTAs */
  --primary-button-text: #FFFFFF; /* White text on dark background */
  --primary-button-hover: #0D0D2A; /* Darker on hover */

  --secondary-button-bg: transparent;
  --secondary-button-text: #151156; /* Brand primary for secondary buttons */
  --secondary-button-border: #151156; /* Brand primary border */
  --secondary-button-hover: #F8F9FC; /* Light background on hover */

  /* Card colors */
  --card-bg: #FFFFFF; /* White cards on light background */
  --card-border: rgba(77, 82, 142, 0.08); /* Subtle border using secondary color */

  /* Hero gradient */
  --hero-gradient: linear-gradient(90deg, #151156 0%, #4D528E 100%);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted-foreground-light: var(--muted-foreground-light);

  --color-brand-primary: var(--brand-primary);
  --color-brand-secondary: var(--brand-secondary);
  --color-brand-accent: var(--brand-accent);

  --color-primary-button-bg: var(--primary-button-bg);
  --color-primary-button-text: var(--primary-button-text);
  --color-primary-button-hover: var(--primary-button-hover);

  --color-secondary-button-bg: var(--secondary-button-bg);
  --color-secondary-button-text: var(--secondary-button-text);
  --color-secondary-button-border: var(--secondary-button-border);
  --color-secondary-button-hover: var(--secondary-button-hover);

  --color-card-bg: var(--card-bg);
  --color-card-border: var(--card-border);

  --color-hero-gradient: var(--hero-gradient);

  --font-sans: var(--font-inter);
  --font-heading: var(--font-poppins);
}

body {
  font-family: var(--font-inter), ui-sans-serif, system-ui, -apple-system,
    "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "Apple Color Emoji",
    "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  background: var(--background);
  color: var(--foreground);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Custom Scrollbar Styles */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--card-border) var(--card-bg);
}

/* Chrome, Edge, Safari */
*::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
*::-webkit-scrollbar-track {
  background: var(--card-bg);
  border-radius: 8px;
}
*::-webkit-scrollbar-thumb {
  background: var(--card-border);
  border-radius: 8px;
  border: 2px solid var(--card-bg);
}
*::-webkit-scrollbar-thumb:hover {
  background: var(--primary-button-hover);
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-poppins), ui-sans-serif, system-ui, -apple-system,
    "Segoe UI", Roboto, "Helvetica Neue", Arial;
}

/* Smooth transitions for interactive elements */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

/* Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Focus styles for better accessibility */
:focus-visible {
  outline: 2px solid var(--brand-primary);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --muted-foreground: var(--foreground);
    --muted-foreground-light: var(--foreground);
  }
}
